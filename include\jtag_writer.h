#ifndef JTAG_WRITER_H
#define JTAG_WRITER_H

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <stdbool.h>
#include <signal.h>

#ifdef _WIN32
    #define strcasecmp _stricmp
    #define strncasecmp _strnicmp
#else
    #include <strings.h>
#endif

#ifdef _WIN32
    #include <winsock2.h>
    #include <ws2tcpip.h>
    #ifdef _MSC_VER
        #pragma comment(lib, "ws2_32.lib")
    #endif
    #define SOCKET_ERROR_CODE WSAGetLastError()
    #define CLOSE_SOCKET closesocket
#else
    #include <sys/socket.h>
    #include <netinet/in.h>
    #include <arpa/inet.h>
    #include <unistd.h>
    #include <errno.h>
    #define SOCKET int
    #define INVALID_SOCKET -1
    #define SOCKET_ERROR -1
    #define SOCKET_ERROR_CODE errno
    #define CLOSE_SOCKET close
#endif

// 版本信息
#define JTAG_WRITER_VERSION_MAJOR 1
#define JTAG_WRITER_VERSION_MINOR 0
#define JTAG_WRITER_VERSION_PATCH 0
#define JTAG_WRITER_VERSION "1.0.0"

// 默认配置
#define DEFAULT_OPENOCD_HOST "127.0.0.1"
#define DEFAULT_OPENOCD_TCL_PORT 6666
#define DEFAULT_OPENOCD_TELNET_PORT 4444
#define DEFAULT_FLASH_BASE_ADDRESS 0x08000000
#define DEFAULT_TIMEOUT_MS 5000

// OpenOCD通信协议
#define OPENOCD_COMMAND_TOKEN "\x1a"

// 缓冲区大小
#define MAX_BUFFER_SIZE 4096
#define MAX_PATH_LENGTH 512
#define MAX_COMMAND_LENGTH 1024
#define MAX_RESPONSE_LENGTH 8192

// 错误代码
typedef enum {
    JTAG_SUCCESS = 0,
    JTAG_ERROR_INVALID_ARGS = -1,
    JTAG_ERROR_FILE_NOT_FOUND = -2,
    JTAG_ERROR_NETWORK_ERROR = -3,
    JTAG_ERROR_OPENOCD_ERROR = -4,
    JTAG_ERROR_CONFIG_ERROR = -5,
    JTAG_ERROR_FLASH_ERROR = -6,
    JTAG_ERROR_MEMORY_ERROR = -7,
    JTAG_ERROR_TIMEOUT = -8,
    JTAG_ERROR_UNKNOWN = -99
} jtag_error_t;

// 日志级别
typedef enum {
    LOG_LEVEL_ERROR = 0,
    LOG_LEVEL_WARN = 1,
    LOG_LEVEL_INFO = 2,
    LOG_LEVEL_DEBUG = 3
} log_level_t;

// 固件文件类型
typedef enum {
    FIRMWARE_TYPE_AUTO = 0,
    FIRMWARE_TYPE_BIN = 1,
    FIRMWARE_TYPE_HEX = 2,
    FIRMWARE_TYPE_ELF = 3,
    FIRMWARE_TYPE_S19 = 4
} firmware_type_t;

// 烧写模式
typedef enum {
    FLASH_MODE_WRITE = 0,
    FLASH_MODE_VERIFY = 1,
    FLASH_MODE_ERASE = 2,
    FLASH_MODE_READ = 3
} flash_mode_t;

// OpenOCD连接配置
typedef struct {
    char host[256];
    int tcl_port;
    int telnet_port;
    int timeout_ms;
    bool use_ssl;
} openocd_config_t;

// 烧写配置
typedef struct {
    char firmware_file[MAX_PATH_LENGTH];
    uint32_t base_address;
    firmware_type_t file_type;
    flash_mode_t mode;
    bool verify_after_write;
    bool erase_before_write;
    bool reset_after_write;
    // Note: target_type removed - OpenOCD handles target configuration
} flash_config_t;

// 进度回调函数类型
typedef void (*progress_callback_t)(int percentage, const char* message);

// 主配置结构
typedef struct {
    openocd_config_t openocd;
    flash_config_t flash;
    log_level_t log_level;
    bool verbose;
    progress_callback_t progress_callback;
} jtag_writer_config_t;

// 全局函数声明
jtag_error_t jtag_writer_init(void);
void jtag_writer_cleanup(void);
const char* jtag_error_string(jtag_error_t error);

#endif // JTAG_WRITER_H
