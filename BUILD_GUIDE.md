# JTAG Writer Build Guide

## Overview
This document provides comprehensive instructions for building the JTAG Writer project using MSYS2/MinGW64 on Windows.

## Prerequisites

### Required Software
1. **MSYS2** - Download from https://www.msys2.org/
   - Install to default location: `C:\msys64`
   - Update package database: `pacman -Syu`

2. **MinGW64 Toolchain**
   ```bash
   # Install in MSYS2 terminal
   pacman -S mingw-w64-x86_64-gcc
   pacman -S mingw-w64-x86_64-make
   pacman -S mingw-w64-x86_64-cmake  # Optional, for CMake builds
   ```

### Verify Installation
```cmd
C:\msys64\mingw64\bin\gcc.exe --version
```
Should output GCC version information.

## Project Structure
```
jtag_writer/
├── src/                    # Source files
│   ├── main.c             # Main entry point
│   ├── core/              # Core functionality
│   ├── network/           # Network communication
│   ├── config/            # Configuration management
│   ├── ui/                # User interface
│   └── utils/             # Utility functions
├── include/               # Header files
├── build/                 # Build output directory
├── CMakeLists.txt         # CMake configuration
├── Makefile              # Make configuration
└── build_msys2.bat       # MSYS2 build script
```

## Build Methods

### Method 1: Using Build Script (Recommended)
```cmd
# Run the automated build script
.\build_msys2.bat
```

### Method 2: Manual Compilation
```cmd
# Set compiler path
set GCC=C:\msys64\mingw64\bin\gcc.exe
set CFLAGS=-Wall -Wextra -std=c99 -Iinclude
set LDFLAGS=-lws2_32

# Create build directories
mkdir build\obj\core build\obj\network build\obj\config build\obj\ui build\obj\utils

# Compile source files
%GCC% %CFLAGS% -c src/main.c -o build/obj/main.o
%GCC% %CFLAGS% -c src/core/jtag_writer.c -o build/obj/core/jtag_writer.o
%GCC% %CFLAGS% -c src/core/flash_operations.c -o build/obj/core/flash_operations.o
%GCC% %CFLAGS% -c src/network/openocd_client.c -o build/obj/network/openocd_client.o
%GCC% %CFLAGS% -c src/config/config_manager.c -o build/obj/config/config_manager.o
%GCC% %CFLAGS% -c src/ui/cli_interface.c -o build/obj/ui/cli_interface.o
%GCC% %CFLAGS% -c src/utils/logger.c -o build/obj/utils/logger.o

# Link executable
%GCC% build/obj/*.o build/obj/*/*.o -o build/jtag_writer.exe %LDFLAGS%
```

### Method 3: Using Makefile
```cmd
# Using MSYS2 environment
C:\msys64\usr\bin\bash.exe -c "make"
```

## Build Output
- **Executable**: `build/jtag_writer.exe`
- **Object files**: `build/obj/` directory
- **Size**: ~495KB

## Validation
Run the test script to validate the build:
```cmd
.\test_build.bat
```

Expected output:
- Executable found and sized correctly
- Version information displays
- Help command responds

## Common Issues and Solutions

### Issue 1: GCC Not Found
**Error**: `'gcc' is not recognized as an internal or external command`
**Solution**: Ensure MSYS2 is installed and use full path to GCC:
```cmd
C:\msys64\mingw64\bin\gcc.exe
```

### Issue 2: Missing ws2_32 Library
**Error**: `undefined reference to WSAStartup`
**Solution**: Add `-lws2_32` to linker flags (already included in build script)

### Issue 3: Include Path Issues
**Error**: `fatal error: 'header.h' file not found`
**Solution**: Ensure `-Iinclude` flag is used during compilation

### Issue 4: Pragma Comment Warnings
**Warning**: `ignoring '#pragma comment'`
**Solution**: These warnings are harmless (MSVC-specific pragmas ignored by GCC)

## Platform-Specific Considerations

### Windows (MSYS2/MinGW64)
- Uses Winsock2 for network functionality
- Requires `ws2_32.lib` linking
- Path separators handled automatically
- Console output supports Unicode

### Dependencies
- **Runtime**: Windows Sockets 2.0 (ws2_32.dll)
- **Build-time**: GCC 14.2.0 or later
- **Standards**: C99 compliance required

## Performance Notes
- Optimized for Windows networking stack
- Efficient memory management
- Minimal external dependencies
- Fast compilation (~30 seconds on modern hardware)

## Next Steps
1. **Testing**: Run comprehensive tests with actual JTAG hardware
2. **Integration**: Test with OpenOCD backend
3. **Packaging**: Create distribution package
4. **Documentation**: Update user documentation

## Troubleshooting
For build issues, check:
1. MSYS2 installation and PATH
2. GCC version compatibility
3. Include file availability
4. Linker flag correctness

Contact: Check project documentation for support information.
