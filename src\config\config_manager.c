#include "config_manager.h"
#include "logger.h"

static bool g_config_initialized = false;

/**
 * Initialize configuration manager
 */
jtag_error_t config_manager_init(void) {
    if (g_config_initialized) {
        return JTAG_SUCCESS;
    }

    g_config_initialized = true;
    return JTAG_SUCCESS;
}

/**
 * Cleanup configuration manager
 */
void config_manager_cleanup(void) {
    g_config_initialized = false;
}

/**
 * Get default configuration
 */
void config_get_default(jtag_writer_config_t* config) {
    if (!config) {
        return;
    }

    memset(config, 0, sizeof(jtag_writer_config_t));

    // OpenOCD configuration
    strcpy(config->openocd.host, DEFAULT_OPENOCD_HOST);
    config->openocd.tcl_port = DEFAULT_OPENOCD_TCL_PORT;
    config->openocd.telnet_port = DEFAULT_OPENOCD_TELNET_PORT;
    config->openocd.timeout_ms = DEFAULT_TIMEOUT_MS;

    // Flash configuration
    config->flash.base_address = 0x08000000; // STM32 default Flash address
    config->flash.file_type = FIRMWARE_TYPE_AUTO;
    config->flash.mode = FLASH_MODE_WRITE;
    config->flash.erase_before_write = true;
    config->flash.verify_after_write = true;
    config->flash.reset_after_write = true;
    strcpy(config->flash.target_type, "stm32f1x");

    // Log configuration
    config->log_level = LOG_LEVEL_INFO;
}

/**
 * Validate configuration
 */
jtag_error_t config_validate(const jtag_writer_config_t* config) {
    if (!config) {
        return JTAG_ERROR_INVALID_ARGS;
    }

    // Validate OpenOCD configuration
    if (strlen(config->openocd.host) == 0) {
        LOG_ERROR("OpenOCD host address cannot be empty");
        return JTAG_ERROR_CONFIG_ERROR;
    }

    if (config->openocd.tcl_port <= 0 || config->openocd.tcl_port > 65535) {
        LOG_ERROR("Invalid OpenOCD TCL port: %d", config->openocd.tcl_port);
        return JTAG_ERROR_CONFIG_ERROR;
    }

    if (config->openocd.telnet_port <= 0 || config->openocd.telnet_port > 65535) {
        LOG_ERROR("Invalid OpenOCD Telnet port: %d", config->openocd.telnet_port);
        return JTAG_ERROR_CONFIG_ERROR;
    }

    // Validate Flash configuration
    if (config->flash.mode == FLASH_MODE_WRITE || config->flash.mode == FLASH_MODE_VERIFY) {
        if (strlen(config->flash.firmware_file) == 0) {
            LOG_ERROR("Firmware file path cannot be empty");
            return JTAG_ERROR_CONFIG_ERROR;
        }

        // Check if file exists
        FILE* file = fopen(config->flash.firmware_file, "rb");
        if (!file) {
            LOG_ERROR("Firmware file does not exist: %s", config->flash.firmware_file);
            return JTAG_ERROR_FILE_NOT_FOUND;
        }
        fclose(file);
    }

    if (strlen(config->flash.target_type) == 0) {
        LOG_ERROR("Target type cannot be empty");
        return JTAG_ERROR_CONFIG_ERROR;
    }

    return JTAG_SUCCESS;
}

/**
 * Print configuration information
 */
void config_print(const jtag_writer_config_t* config) {
    if (!config) {
        return;
    }

    printf("=== JTAG Writer Configuration ===\n");
    printf("OpenOCD Configuration:\n");
    printf("  Host address: %s\n", config->openocd.host);
    printf("  TCL port: %d\n", config->openocd.tcl_port);
    printf("  Telnet port: %d\n", config->openocd.telnet_port);
    printf("  Timeout: %d ms\n", config->openocd.timeout_ms);

    printf("Flash Configuration:\n");
    printf("  Firmware file: %s\n", config->flash.firmware_file);
    printf("  Target type: %s\n", config->flash.target_type);
    printf("  Base address: 0x%08x\n", config->flash.base_address);
    printf("  File type: %d\n", config->flash.file_type);
    printf("  Flash mode: %d\n", config->flash.mode);
    printf("  Erase before write: %s\n", config->flash.erase_before_write ? "Yes" : "No");
    printf("  Verify after write: %s\n", config->flash.verify_after_write ? "Yes" : "No");
    printf("  Reset after write: %s\n", config->flash.reset_after_write ? "Yes" : "No");

    printf("Log Configuration:\n");
    printf("  Log level: %s\n", logger_level_string(config->log_level));
    printf("================================\n");
}

/**
 * Set OpenOCD host address
 */
jtag_error_t config_set_openocd_host(jtag_writer_config_t* config, const char* host) {
    if (!config || !host) {
        return JTAG_ERROR_INVALID_ARGS;
    }

    if (strlen(host) >= sizeof(config->openocd.host)) {
        return JTAG_ERROR_INVALID_ARGS;
    }

    strcpy(config->openocd.host, host);
    return JTAG_SUCCESS;
}

/**
 * Set OpenOCD ports
 */
jtag_error_t config_set_openocd_ports(jtag_writer_config_t* config, int tcl_port, int telnet_port) {
    if (!config) {
        return JTAG_ERROR_INVALID_ARGS;
    }

    if (tcl_port <= 0 || tcl_port > 65535 || telnet_port <= 0 || telnet_port > 65535) {
        return JTAG_ERROR_INVALID_ARGS;
    }

    config->openocd.tcl_port = tcl_port;
    config->openocd.telnet_port = telnet_port;
    return JTAG_SUCCESS;
}

/**
 * Set firmware file
 */
jtag_error_t config_set_firmware_file(jtag_writer_config_t* config, const char* firmware_file) {
    if (!config || !firmware_file) {
        return JTAG_ERROR_INVALID_ARGS;
    }

    if (strlen(firmware_file) >= sizeof(config->flash.firmware_file)) {
        return JTAG_ERROR_INVALID_ARGS;
    }

    strcpy(config->flash.firmware_file, firmware_file);
    return JTAG_SUCCESS;
}

/**
 * Set target type
 */
jtag_error_t config_set_target_type(jtag_writer_config_t* config, const char* target_type) {
    if (!config || !target_type) {
        return JTAG_ERROR_INVALID_ARGS;
    }

    if (strlen(target_type) >= sizeof(config->flash.target_type)) {
        return JTAG_ERROR_INVALID_ARGS;
    }

    strcpy(config->flash.target_type, target_type);
    return JTAG_SUCCESS;
}

/**
 * Set base address
 */
jtag_error_t config_set_base_address(jtag_writer_config_t* config, uint32_t base_address) {
    if (!config) {
        return JTAG_ERROR_INVALID_ARGS;
    }
    
    config->flash.base_address = base_address;
    return JTAG_SUCCESS;
}

/**
 * Set file type
 */
jtag_error_t config_set_file_type(jtag_writer_config_t* config, firmware_type_t file_type) {
    if (!config) {
        return JTAG_ERROR_INVALID_ARGS;
    }

    config->flash.file_type = file_type;
    return JTAG_SUCCESS;
}

/**
 * Set flash mode
 */
jtag_error_t config_set_flash_mode(jtag_writer_config_t* config, flash_mode_t mode) {
    if (!config) {
        return JTAG_ERROR_INVALID_ARGS;
    }

    config->flash.mode = mode;
    return JTAG_SUCCESS;
}

/**
 * Set log level
 */
jtag_error_t config_set_log_level(jtag_writer_config_t* config, log_level_t log_level) {
    if (!config) {
        return JTAG_ERROR_INVALID_ARGS;
    }

    config->log_level = log_level;
    return JTAG_SUCCESS;
}

/**
 * Update configuration from command line arguments
 */
jtag_error_t config_update_from_args(jtag_writer_config_t* config, int argc, char* argv[]) {
    if (!config || !argv) {
        return JTAG_ERROR_INVALID_ARGS;
    }

    // Simplified command line parsing, should use getopt or similar library
    for (int i = 1; i < argc; i++) {
        if (strcmp(argv[i], "-h") == 0 || strcmp(argv[i], "--host") == 0) {
            if (i + 1 < argc) {
                config_set_openocd_host(config, argv[++i]);
            }
        } else if (strcmp(argv[i], "-p") == 0 || strcmp(argv[i], "--port") == 0) {
            if (i + 1 < argc) {
                int port = atoi(argv[++i]);
                config->openocd.tcl_port = port;
            }
        } else if (strcmp(argv[i], "-f") == 0 || strcmp(argv[i], "--file") == 0) {
            if (i + 1 < argc) {
                config_set_firmware_file(config, argv[++i]);
            }
        } else if (strcmp(argv[i], "-t") == 0 || strcmp(argv[i], "--target") == 0) {
            if (i + 1 < argc) {
                config_set_target_type(config, argv[++i]);
            }
        } else if (strcmp(argv[i], "-a") == 0 || strcmp(argv[i], "--address") == 0) {
            if (i + 1 < argc) {
                uint32_t addr = (uint32_t)strtoul(argv[++i], NULL, 0);
                config_set_base_address(config, addr);
            }
        } else if (strcmp(argv[i], "--erase") == 0) {
            config->flash.mode = FLASH_MODE_ERASE;
        } else if (strcmp(argv[i], "--verify") == 0) {
            config->flash.mode = FLASH_MODE_VERIFY;
        } else if (strcmp(argv[i], "--read") == 0) {
            config->flash.mode = FLASH_MODE_READ;
        } else if (strcmp(argv[i], "--no-erase") == 0) {
            config->flash.erase_before_write = false;
        } else if (strcmp(argv[i], "--no-verify") == 0) {
            config->flash.verify_after_write = false;
        } else if (strcmp(argv[i], "--no-reset") == 0) {
            config->flash.reset_after_write = false;
        }
    }

    return JTAG_SUCCESS;
}

/**
 * Load configuration file (simplified version, should use JSON parsing)
 */
jtag_error_t config_load(const char* config_file, jtag_writer_config_t* config) {
    if (!config_file || !config) {
        return JTAG_ERROR_INVALID_ARGS;
    }

    FILE* file = fopen(config_file, "r");
    if (!file) {
        LOG_WARN("Cannot open configuration file: %s", config_file);
        return JTAG_ERROR_FILE_NOT_FOUND;
    }

    // Simplified configuration file parsing
    // Should use JSON parsing library like cJSON
    char line[256];
    while (fgets(line, sizeof(line), file)) {
        // Remove newline character
        char* newline = strchr(line, '\n');
        if (newline) *newline = '\0';

        // Skip comments and empty lines
        if (line[0] == '#' || line[0] == '\0') {
            continue;
        }

        // Parse key-value pairs
        char* equals = strchr(line, '=');
        if (equals) {
            *equals = '\0';
            char* key = line;
            char* value = equals + 1;

            // Remove leading and trailing spaces
            while (*key == ' ') key++;
            while (*value == ' ') value++;

            // Parse configuration items
            if (strcmp(key, "openocd_host") == 0) {
                config_set_openocd_host(config, value);
            } else if (strcmp(key, "openocd_tcl_port") == 0) {
                config->openocd.tcl_port = atoi(value);
            } else if (strcmp(key, "openocd_telnet_port") == 0) {
                config->openocd.telnet_port = atoi(value);
            } else if (strcmp(key, "firmware_file") == 0) {
                config_set_firmware_file(config, value);
            } else if (strcmp(key, "target_type") == 0) {
                config_set_target_type(config, value);
            } else if (strcmp(key, "base_address") == 0) {
                uint32_t addr = (uint32_t)strtoul(value, NULL, 0);
                config_set_base_address(config, addr);
            } else if (strcmp(key, "log_level") == 0) {
                log_level_t level = logger_parse_level(value);
                config_set_log_level(config, level);
            }
        }
    }

    fclose(file);
    LOG_INFO("Successfully loaded configuration file: %s", config_file);
    return JTAG_SUCCESS;
}

/**
 * Save configuration file (simplified version)
 */
jtag_error_t config_save(const char* config_file, const jtag_writer_config_t* config) {
    if (!config_file || !config) {
        return JTAG_ERROR_INVALID_ARGS;
    }

    FILE* file = fopen(config_file, "w");
    if (!file) {
        LOG_ERROR("Cannot create configuration file: %s", config_file);
        return JTAG_ERROR_CONFIG_ERROR;
    }

    fprintf(file, "# JTAG Writer Configuration File\n");
    fprintf(file, "# Auto-generated, please modify with caution\n\n");

    fprintf(file, "# OpenOCD Configuration\n");
    fprintf(file, "openocd_host=%s\n", config->openocd.host);
    fprintf(file, "openocd_tcl_port=%d\n", config->openocd.tcl_port);
    fprintf(file, "openocd_telnet_port=%d\n", config->openocd.telnet_port);
    fprintf(file, "\n");

    fprintf(file, "# Flash Configuration\n");
    fprintf(file, "firmware_file=%s\n", config->flash.firmware_file);
    fprintf(file, "target_type=%s\n", config->flash.target_type);
    fprintf(file, "base_address=0x%08x\n", config->flash.base_address);
    fprintf(file, "\n");

    fprintf(file, "# Log Configuration\n");
    fprintf(file, "log_level=%s\n", logger_level_string(config->log_level));

    fclose(file);
    LOG_INFO("Successfully saved configuration file: %s", config_file);
    return JTAG_SUCCESS;
}
