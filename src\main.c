#include "jtag_writer.h"
#include "openocd_client.h"
#include "flash_operations.h"
#include "config_manager.h"
#include "cli_interface.h"
#include "logger.h"

static jtag_writer_config_t g_config;
static openocd_client_t* g_client = NULL;

/**
 * Cleanup resources
 */
static void cleanup_resources(void) {
    if (g_client) {
        openocd_client_disconnect(g_client);
        openocd_client_destroy(g_client);
        g_client = NULL;
    }

    logger_cleanup();
    config_manager_cleanup();
    cli_cleanup();
    jtag_writer_cleanup();
}

/**
 * Signal handler function
 */
static void signal_handler(int sig) {
    (void)sig; // Avoid unused parameter warning
    printf("\nProgram interrupted, cleaning up resources...\n");
    cleanup_resources();
    exit(EXIT_FAILURE);
}

/**
 * Initialize system
 */
static jtag_error_t initialize_system(void) {
    jtag_error_t ret;

    // Initialize JTAG Writer
    ret = jtag_writer_init();
    if (ret != JTAG_SUCCESS) {
        fprintf(stderr, "Failed to initialize JTAG Writer: %s\n", jtag_error_string(ret));
        return ret;
    }

    // Initialize CLI
    ret = cli_init();
    if (ret != JTAG_SUCCESS) {
        fprintf(stderr, "Failed to initialize CLI: %s\n", jtag_error_string(ret));
        return ret;
    }

    // Initialize configuration manager
    ret = config_manager_init();
    if (ret != JTAG_SUCCESS) {
        fprintf(stderr, "Failed to initialize configuration manager: %s\n", jtag_error_string(ret));
        return ret;
    }

    return JTAG_SUCCESS;
}

/**
 * Perform flash operation
 */
static jtag_error_t perform_flash_operation(void) {
    jtag_error_t ret;
    flash_result_t result;

    // Create OpenOCD client
    g_client = openocd_client_create(&g_config.openocd);
    if (!g_client) {
        cli_show_error(JTAG_ERROR_MEMORY_ERROR, "Failed to create OpenOCD client");
        return JTAG_ERROR_MEMORY_ERROR;
    }

    // Connect to OpenOCD
    cli_show_info("Connecting to OpenOCD server...");
    ret = openocd_client_connect(g_client);
    if (ret != JTAG_SUCCESS) {
        cli_show_error(ret, "Failed to connect to OpenOCD server");
        return ret;
    }

    cli_show_success("Successfully connected to OpenOCD server");

    // Get version information
    char version[256];
    ret = openocd_client_get_version(g_client, version, sizeof(version));
    if (ret == JTAG_SUCCESS) {
        cli_show_info(version);
    }

    // Initialize target
    cli_show_info("Initializing target...");
    ret = openocd_client_init_target(g_client);
    if (ret != JTAG_SUCCESS) {
        cli_show_error(ret, "Failed to initialize target");
        return ret;
    }

    // Execute different operations based on mode
    switch (g_config.flash.mode) {
        case FLASH_MODE_WRITE:
            cli_show_info("Starting firmware programming...");
            result = flash_write_file(g_client, &g_config.flash, cli_progress_callback);
            break;

        case FLASH_MODE_VERIFY:
            cli_show_info("Starting firmware verification...");
            result = flash_verify_file(g_client, &g_config.flash, cli_progress_callback);
            break;

        case FLASH_MODE_ERASE:
            cli_show_info("Starting Flash erase...");
            result = flash_mass_erase(g_client, 0, cli_progress_callback);
            break;

        case FLASH_MODE_READ:
            cli_show_info("Starting Flash read...");
            result = flash_read_to_file(g_client, 0, g_config.flash.base_address,
                                      0, "flash_dump.bin", cli_progress_callback);
            break;

        default:
            cli_show_error(JTAG_ERROR_INVALID_ARGS, "Unsupported flash mode");
            return JTAG_ERROR_INVALID_ARGS;
    }

    // Display results
    cli_show_flash_result(&result);

    if (result.success) {
        cli_show_success("Operation completed");

        // Reset if needed
        if (g_config.flash.reset_after_write && g_config.flash.mode == FLASH_MODE_WRITE) {
            cli_show_info("Resetting target...");
            ret = openocd_client_reset_target(g_client, false);
            if (ret == JTAG_SUCCESS) {
                cli_show_success("Target reset completed");
            } else {
                cli_show_warning("Target reset failed");
            }
        }

        return JTAG_SUCCESS;
    } else {
        cli_show_error(JTAG_ERROR_FLASH_ERROR, result.error_message);
        return JTAG_ERROR_FLASH_ERROR;
    }
}

/**
 * Main function
 */
int main(int argc, char* argv[]) {
    jtag_error_t ret;
    cli_args_t args;

    // Set up signal handling
#ifdef _WIN32
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
#else
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    signal(SIGQUIT, signal_handler);
#endif

    // Initialize system
    ret = initialize_system();
    if (ret != JTAG_SUCCESS) {
        return EXIT_FAILURE;
    }

    // Parse command line arguments
    ret = cli_parse_args(argc, argv, &args);
    if (ret != JTAG_SUCCESS) {
        cleanup_resources();
        return EXIT_FAILURE;
    }

    // Handle help and version requests
    if (args.help_requested) {
        cli_show_help(argv[0]);
        cleanup_resources();
        return EXIT_SUCCESS;
    }

    if (args.version_requested) {
        cli_show_version();
        cleanup_resources();
        return EXIT_SUCCESS;
    }

    // Load configuration
    config_get_default(&g_config);

    if (strlen(args.config_file) > 0) {
        ret = config_load(args.config_file, &g_config);
        if (ret != JTAG_SUCCESS) {
            cli_show_warning("Failed to load configuration file, using default configuration");
        }
    }

    // Update configuration from command line arguments
    ret = config_update_from_args(&g_config, argc, argv);
    if (ret != JTAG_SUCCESS) {
        cli_show_error(ret, "Failed to update configuration");
        cleanup_resources();
        return EXIT_FAILURE;
    }

    // Validate configuration
    ret = config_validate(&g_config);
    if (ret != JTAG_SUCCESS) {
        cli_show_error(ret, "Configuration validation failed");
        cleanup_resources();
        return EXIT_FAILURE;
    }

    // Initialize logging system
    logger_config_t log_config = {
        .level = g_config.log_level,
        .output = args.verbose ? LOG_OUTPUT_BOTH : LOG_OUTPUT_CONSOLE,
        .timestamp_enabled = true,
        .color_enabled = true
    };
    strcpy(log_config.log_file, DEFAULT_LOG_FILE);

    ret = logger_init(&log_config);
    if (ret != JTAG_SUCCESS) {
        cli_show_warning("Failed to initialize logging system");
    }

    // Display configuration information (if verbose mode)
    if (args.verbose) {
        cli_show_config(&g_config);
    }

    // Perform flash operation
    ret = perform_flash_operation();

    // Cleanup resources
    cleanup_resources();
    
    return (ret == JTAG_SUCCESS) ? EXIT_SUCCESS : EXIT_FAILURE;
}
