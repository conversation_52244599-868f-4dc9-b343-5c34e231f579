#ifndef CONFIG_MANAGER_H
#define CONFIG_MANAGER_H

#include "jtag_writer.h"

// 配置文件路径
#define DEFAULT_CONFIG_FILE "config/jtag_writer.json"
#define USER_CONFIG_FILE "jtag_writer_user.json"

// 函数声明

/**
 * 初始化配置管理器
 * @return 错误代码
 */
jtag_error_t config_manager_init(void);

/**
 * 清理配置管理器
 */
void config_manager_cleanup(void);

/**
 * 加载配置文件
 * @param config_file 配置文件路径，NULL表示使用默认配置
 * @param config 配置结构
 * @return 错误代码
 */
jtag_error_t config_load(const char* config_file, jtag_writer_config_t* config);

/**
 * 保存配置文件
 * @param config_file 配置文件路径
 * @param config 配置结构
 * @return 错误代码
 */
jtag_error_t config_save(const char* config_file, const jtag_writer_config_t* config);

/**
 * 获取默认配置
 * @param config 配置结构
 */
void config_get_default(jtag_writer_config_t* config);

/**
 * 验证配置有效性
 * @param config 配置结构
 * @return 错误代码
 */
jtag_error_t config_validate(const jtag_writer_config_t* config);

/**
 * 打印配置信息
 * @param config 配置结构
 */
void config_print(const jtag_writer_config_t* config);

/**
 * 从命令行参数更新配置
 * @param config 配置结构
 * @param argc 参数数量
 * @param argv 参数数组
 * @return 错误代码
 */
jtag_error_t config_update_from_args(jtag_writer_config_t* config, int argc, char* argv[]);

/**
 * 设置OpenOCD主机地址
 * @param config 配置结构
 * @param host 主机地址
 * @return 错误代码
 */
jtag_error_t config_set_openocd_host(jtag_writer_config_t* config, const char* host);

/**
 * 设置OpenOCD端口
 * @param config 配置结构
 * @param tcl_port TCL端口
 * @param telnet_port Telnet端口
 * @return 错误代码
 */
jtag_error_t config_set_openocd_ports(jtag_writer_config_t* config, int tcl_port, int telnet_port);

/**
 * 设置固件文件
 * @param config 配置结构
 * @param firmware_file 固件文件路径
 * @return 错误代码
 */
jtag_error_t config_set_firmware_file(jtag_writer_config_t* config, const char* firmware_file);

// Note: config_set_target_type function removed - OpenOCD handles target configuration

/**
 * 设置基地址
 * @param config 配置结构
 * @param base_address 基地址
 * @return 错误代码
 */
jtag_error_t config_set_base_address(jtag_writer_config_t* config, uint32_t base_address);

/**
 * 设置文件类型
 * @param config 配置结构
 * @param file_type 文件类型
 * @return 错误代码
 */
jtag_error_t config_set_file_type(jtag_writer_config_t* config, firmware_type_t file_type);

/**
 * 设置烧写模式
 * @param config 配置结构
 * @param mode 烧写模式
 * @return 错误代码
 */
jtag_error_t config_set_flash_mode(jtag_writer_config_t* config, flash_mode_t mode);

/**
 * 设置日志级别
 * @param config 配置结构
 * @param log_level 日志级别
 * @return 错误代码
 */
jtag_error_t config_set_log_level(jtag_writer_config_t* config, log_level_t log_level);

#endif // CONFIG_MANAGER_H
