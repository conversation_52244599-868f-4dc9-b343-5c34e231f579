cmake_minimum_required(VERSION 3.10)
project(jtag_writer VERSION 1.0.0 LANGUAGES C)

# Set C standard
set(CMAKE_C_STANDARD 99)
set(CMAKE_C_STANDARD_REQUIRED ON)

# Set compile options
if(MSVC)
    add_compile_options(/W4)
    add_definitions(-D_CRT_SECURE_NO_WARNINGS)
else()
    add_compile_options(-Wall -Wextra -pedantic)
endif()

# Set output directories
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# Include directories
include_directories(include)

# Find dependencies
if(WIN32)
    # Windows platform requires linking ws2_32 library
    set(PLATFORM_LIBS ws2_32)
else()
    # Unix platform
    set(PLATFORM_LIBS)
endif()

# Source files (only include existing files)
set(CORE_SOURCES
    src/core/jtag_writer.c
    src/core/flash_operations.c
)

set(NETWORK_SOURCES
    src/network/openocd_client.c
)

set(CONFIG_SOURCES
    src/config/config_manager.c
)

set(UI_SOURCES
    src/ui/cli_interface.c
)

set(UTILS_SOURCES
    src/utils/logger.c
)

set(MAIN_SOURCE
    src/main.c
)

# Create executable
add_executable(jtag_writer
    ${MAIN_SOURCE}
    ${CORE_SOURCES}
    ${NETWORK_SOURCES}
    ${CONFIG_SOURCES}
    ${UI_SOURCES}
    ${UTILS_SOURCES}
)

# Link libraries
target_link_libraries(jtag_writer ${PLATFORM_LIBS})

# Install rules
install(TARGETS jtag_writer DESTINATION bin)
install(DIRECTORY config/ DESTINATION share/jtag_writer/config)
install(DIRECTORY docs/ DESTINATION share/jtag_writer/docs)

# Tests (temporarily commented out, test files not yet created)
# enable_testing()

# Add test executable
# add_executable(test_jtag_writer
#     tests/test_main.c
#     tests/test_openocd_client.c
#     tests/test_config_manager.c
#     ${CORE_SOURCES}
#     ${NETWORK_SOURCES}
#     ${CONFIG_SOURCES}
#     ${UTILS_SOURCES}
# )

# target_link_libraries(test_jtag_writer ${PLATFORM_LIBS})

# add_test(NAME jtag_writer_tests COMMAND test_jtag_writer)

# Debug information
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    add_definitions(-DDEBUG)
    if(NOT MSVC)
        add_compile_options(-g)
    endif()
endif()

# Release information
if(CMAKE_BUILD_TYPE STREQUAL "Release")
    add_definitions(-DNDEBUG)
    if(NOT MSVC)
        add_compile_options(-O2)
    endif()
endif()
