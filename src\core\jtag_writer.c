#include "jtag_writer.h"
#include "logger.h"

static bool g_initialized = false;

/**
 * Initialize JTAG Writer
 */
jtag_error_t jtag_writer_init(void) {
    if (g_initialized) {
        return JTAG_SUCCESS;
    }

#ifdef _WIN32
    // Initialize Winsock
    WSADATA wsaData;
    int result = WSAStartup(MAKEWORD(2, 2), &wsaData);
    if (result != 0) {
        return JTAG_ERROR_NETWORK_ERROR;
    }
#endif

    g_initialized = true;
    return JTAG_SUCCESS;
}

/**
 * Cleanup JTAG Writer
 */
void jtag_writer_cleanup(void) {
    if (!g_initialized) {
        return;
    }

#ifdef _WIN32
    WSACleanup();
#endif

    g_initialized = false;
}

/**
 * Get error string
 */
const char* jtag_error_string(jtag_error_t error) {
    switch (error) {
        case JTAG_SUCCESS:
            return "Success";
        case JTAG_ERROR_INVALID_ARGS:
            return "Invalid arguments";
        case JTAG_ERROR_FILE_NOT_FOUND:
            return "File not found";
        case JTAG_ERROR_NETWORK_ERROR:
            return "Network error";
        case JTAG_ERROR_OPENOCD_ERROR:
            return "OpenOCD error";
        case JTAG_ERROR_CONFIG_ERROR:
            return "Configuration error";
        case JTAG_ERROR_FLASH_ERROR:
            return "Flash operation error";
        case JTAG_ERROR_MEMORY_ERROR:
            return "Memory error";
        case JTAG_ERROR_TIMEOUT:
            return "Timeout error";
        case JTAG_ERROR_UNKNOWN:
        default:
            return "Unknown error";
    }
}
