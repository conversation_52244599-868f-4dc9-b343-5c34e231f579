#include "cli_interface.h"
#include "config_manager.h"
#include "logger.h"

// Command line options definition
static const cli_option_t g_options[] = {
    {'h', "help",        false, "Show help information"},
    {'v', "version",     false, "Show version information"},
    {'V', "verbose",     false, "Verbose output mode"},
    {'c', "config",      true,  "Specify configuration file"},
    {'f', "file",        true,  "Specify firmware file"},
    {'t', "target",      true,  "Specify target type"},
    {'H', "host",        true,  "Specify OpenOCD host address"},
    {'p', "port",        true,  "Specify OpenOCD TCL port"},
    {'P', "telnet-port", true,  "Specify OpenOCD Telnet port"},
    {'a', "address",     true,  "Specify base address"},
    {'T', "type",        true,  "Specify file type (bin/hex/elf/s19)"},
    {'m', "mode",        true,  "Specify operation mode (write/verify/erase/read)"},
    {'l', "log-level",   true,  "Specify log level (error/warn/info/debug)"},
    {0,   "erase",       false, "Erase Flash only"},
    {0,   "verify",      false, "Verify firmware only"},
    {0,   "read",        false, "Read Flash content"},
    {0,   "no-erase",    false, "Don't erase before write"},
    {0,   "no-verify",   false, "Don't verify after write"},
    {0,   "no-reset",    false, "Don't reset after write"},
    {0,   NULL,          false, NULL}
};

/**
 * Initialize CLI interface
 */
jtag_error_t cli_init(void) {
    return JTAG_SUCCESS;
}

/**
 * Cleanup CLI interface
 */
void cli_cleanup(void) {
    // Cleanup resources
}

/**
 * Show help information
 */
void cli_show_help(const char* program_name) {
    printf("JTAG Writer - OpenOCD Frontend Flash Tool\n\n");
    printf("Usage: %s [options] [firmware_file]\n\n", program_name);

    printf("Options:\n");
    for (int i = 0; g_options[i].long_opt || g_options[i].short_opt; i++) {
        const cli_option_t* opt = &g_options[i];

        if (opt->short_opt && opt->long_opt) {
            printf("  -%c, --%-15s", opt->short_opt, opt->long_opt);
        } else if (opt->long_opt) {
            printf("      --%-15s", opt->long_opt);
        } else {
            continue;
        }

        if (opt->has_arg) {
            printf(" <arg>");
        }

        printf("  %s\n", opt->description);
    }

    printf("\nExamples:\n");
    cli_show_examples();

    printf("\nSupported target types:\n");
    cli_show_supported_targets();

    printf("\nSupported file types:\n");
    cli_show_supported_file_types();
}

/**
 * Show version information
 */
void cli_show_version(void) {
    printf("JTAG Writer v%s\n", JTAG_WRITER_VERSION);
    printf("OpenOCD-based firmware flash tool\n");
    printf("Build time: %s %s\n", __DATE__, __TIME__);
}

/**
 * Show usage examples
 */
void cli_show_examples(void) {
    printf("  # Flash firmware to STM32F103\n");
    printf("  %s -f firmware.bin -t stm32f1x -a 0x08000000\n\n", "jtag_writer");

    printf("  # Verify flashed firmware\n");
    printf("  %s --verify -f firmware.bin -t stm32f1x\n\n", "jtag_writer");

    printf("  # Erase entire Flash\n");
    printf("  %s --erase -t stm32f1x\n\n", "jtag_writer");

    printf("  # Connect to remote OpenOCD server\n");
    printf("  %s -H ************* -p 6666 -f firmware.hex\n\n", "jtag_writer");

    printf("  # Use configuration file\n");
    printf("  %s -c config.conf -f firmware.elf\n", "jtag_writer");
}

/**
 * Show supported target types
 */
void cli_show_supported_targets(void) {
    printf("  stm32f1x    - STM32F1 series\n");
    printf("  stm32f2x    - STM32F2 series\n");
    printf("  stm32f4x    - STM32F4 series\n");
    printf("  stm32f7x    - STM32F7 series\n");
    printf("  stm32h7x    - STM32H7 series\n");
    printf("  stm32l4x    - STM32L4 series\n");
    printf("  at91sam3    - Atmel SAM3 series\n");
    printf("  at91sam4    - Atmel SAM4 series\n");
    printf("  lpc2000     - NXP LPC2000 series\n");
    printf("  lpc1700     - NXP LPC1700 series\n");
}

/**
 * Show supported file types
 */
void cli_show_supported_file_types(void) {
    printf("  bin         - Binary file\n");
    printf("  hex         - Intel HEX file\n");
    printf("  elf         - ELF executable file\n");
    printf("  s19         - Motorola S-record file\n");
    printf("  auto        - Auto detect (based on file extension)\n");
}

/**
 * Progress display callback function
 */
void cli_progress_callback(int percentage, const char* message) {
    static int last_percentage = -1;

    if (percentage != last_percentage) {
        printf("\r[");
        int pos = percentage / 2; // 50-character progress bar
        for (int i = 0; i < 50; i++) {
            if (i < pos) {
                printf("=");
            } else if (i == pos) {
                printf(">");
            } else {
                printf(" ");
            }
        }
        printf("] %3d%% %s", percentage, message ? message : "");
        fflush(stdout);

        if (percentage >= 100) {
            printf("\n");
        }

        last_percentage = percentage;
    }
}

/**
 * Show error information
 */
void cli_show_error(jtag_error_t error, const char* message) {
    fprintf(stderr, "\033[31mError\033[0m: %s", message ? message : jtag_error_string(error));
    if (message && error != JTAG_SUCCESS) {
        fprintf(stderr, " (%s)", jtag_error_string(error));
    }
    fprintf(stderr, "\n");
}

/**
 * Show success information
 */
void cli_show_success(const char* message) {
    printf("\033[32mSuccess\033[0m: %s\n", message);
}

/**
 * Show warning information
 */
void cli_show_warning(const char* message) {
    printf("\033[33mWarning\033[0m: %s\n", message);
}

/**
 * Show information
 */
void cli_show_info(const char* message) {
    printf("\033[36mInfo\033[0m: %s\n", message);
}

/**
 * Ask user for confirmation
 */
bool cli_ask_confirmation(const char* message) {
    printf("%s (y/N): ", message);
    fflush(stdout);

    char response[10];
    if (fgets(response, sizeof(response), stdin)) {
        return (response[0] == 'y' || response[0] == 'Y');
    }

    return false;
}

/**
 * Wait for user key press
 */
void cli_wait_for_key(const char* message) {
    printf("%s", message ? message : "Press any key to continue...");
    fflush(stdout);
    getchar();
}

/**
 * Show Flash bank information
 */
void cli_show_flash_banks(const flash_bank_info_t* banks, int num_banks) {
    if (!banks || num_banks <= 0) {
        printf("No Flash bank information found\n");
        return;
    }

    printf("Flash Banks:\n");
    printf("ID  Driver       Base Addr   Size\n");
    printf("--- ------------ ----------- -----------\n");

    for (int i = 0; i < num_banks; i++) {
        const flash_bank_info_t* bank = &banks[i];
        printf("%-3d %-12s 0x%08x  %u KB\n",
               bank->bank_id, bank->driver_name,
               bank->base_address, bank->size / 1024);
    }
}

/**
 * Show flash operation result
 */
void cli_show_flash_result(const flash_result_t* result) {
    if (!result) {
        return;
    }

    if (result->success) {
        printf("\nOperation completed successfully:\n");
        printf("  Bytes processed: %u\n", result->bytes_processed);
        printf("  Total bytes: %u\n", result->total_bytes);
        printf("  Elapsed time: %.2f seconds\n", result->elapsed_time);

        if (result->total_bytes > 0 && result->elapsed_time > 0) {
            double speed = result->bytes_processed / result->elapsed_time / 1024.0;
            printf("  Average speed: %.2f KB/s\n", speed);
        }
    } else {
        printf("\nOperation failed:\n");
        printf("  Error message: %s\n", result->error_message);
        if (result->bytes_processed > 0) {
            printf("  Bytes processed: %u\n", result->bytes_processed);
        }
    }
}

/**
 * Show configuration information
 */
void cli_show_config(const jtag_writer_config_t* config) {
    if (!config) {
        return;
    }

    config_print(config);
}

/**
 * Parse command line arguments
 */
jtag_error_t cli_parse_args(int argc, char* argv[], cli_args_t* args) {
    if (!argv || !args) {
        return JTAG_ERROR_INVALID_ARGS;
    }

    // Initialize argument structure
    memset(args, 0, sizeof(cli_args_t));
    args->openocd_tcl_port = -1;
    args->openocd_telnet_port = -1;
    args->file_type = FIRMWARE_TYPE_AUTO;
    args->flash_mode = FLASH_MODE_WRITE;
    args->log_level = LOG_LEVEL_INFO;
    args->erase_before_write = true;
    args->verify_after_write = true;
    args->reset_after_write = true;

    // Simplified command line parsing
    for (int i = 1; i < argc; i++) {
        char* arg = argv[i];

        if (strcmp(arg, "-h") == 0 || strcmp(arg, "--help") == 0) {
            args->help_requested = true;
        } else if (strcmp(arg, "-v") == 0 || strcmp(arg, "--version") == 0) {
            args->version_requested = true;
        } else if (strcmp(arg, "-V") == 0 || strcmp(arg, "--verbose") == 0) {
            args->verbose = true;
        } else if (strcmp(arg, "-c") == 0 || strcmp(arg, "--config") == 0) {
            if (i + 1 < argc) {
                strncpy(args->config_file, argv[++i], sizeof(args->config_file) - 1);
            } else {
                cli_show_error(JTAG_ERROR_INVALID_ARGS, "Option -c requires an argument");
                return JTAG_ERROR_INVALID_ARGS;
            }
        } else if (strcmp(arg, "-f") == 0 || strcmp(arg, "--file") == 0) {
            if (i + 1 < argc) {
                strncpy(args->firmware_file, argv[++i], sizeof(args->firmware_file) - 1);
            } else {
                cli_show_error(JTAG_ERROR_INVALID_ARGS, "Option -f requires an argument");
                return JTAG_ERROR_INVALID_ARGS;
            }
        } else if (strcmp(arg, "-t") == 0 || strcmp(arg, "--target") == 0) {
            if (i + 1 < argc) {
                strncpy(args->target_type, argv[++i], sizeof(args->target_type) - 1);
            } else {
                cli_show_error(JTAG_ERROR_INVALID_ARGS, "Option -t requires an argument");
                return JTAG_ERROR_INVALID_ARGS;
            }
        } else if (strcmp(arg, "-H") == 0 || strcmp(arg, "--host") == 0) {
            if (i + 1 < argc) {
                strncpy(args->openocd_host, argv[++i], sizeof(args->openocd_host) - 1);
            } else {
                cli_show_error(JTAG_ERROR_INVALID_ARGS, "Option -H requires an argument");
                return JTAG_ERROR_INVALID_ARGS;
            }
        } else if (strcmp(arg, "-p") == 0 || strcmp(arg, "--port") == 0) {
            if (i + 1 < argc) {
                args->openocd_tcl_port = atoi(argv[++i]);
            } else {
                cli_show_error(JTAG_ERROR_INVALID_ARGS, "Option -p requires an argument");
                return JTAG_ERROR_INVALID_ARGS;
            }
        } else if (strcmp(arg, "-P") == 0 || strcmp(arg, "--telnet-port") == 0) {
            if (i + 1 < argc) {
                args->openocd_telnet_port = atoi(argv[++i]);
            } else {
                cli_show_error(JTAG_ERROR_INVALID_ARGS, "Option -P requires an argument");
                return JTAG_ERROR_INVALID_ARGS;
            }
        } else if (strcmp(arg, "-a") == 0 || strcmp(arg, "--address") == 0) {
            if (i + 1 < argc) {
                jtag_error_t ret = cli_parse_address(argv[++i], &args->base_address);
                if (ret != JTAG_SUCCESS) {
                    cli_show_error(ret, "Invalid address format");
                    return ret;
                }
            } else {
                cli_show_error(JTAG_ERROR_INVALID_ARGS, "Option -a requires an argument");
                return JTAG_ERROR_INVALID_ARGS;
            }
        } else if (strcmp(arg, "-T") == 0 || strcmp(arg, "--type") == 0) {
            if (i + 1 < argc) {
                args->file_type = cli_parse_file_type(argv[++i]);
            } else {
                cli_show_error(JTAG_ERROR_INVALID_ARGS, "Option -T requires an argument");
                return JTAG_ERROR_INVALID_ARGS;
            }
        } else if (strcmp(arg, "-m") == 0 || strcmp(arg, "--mode") == 0) {
            if (i + 1 < argc) {
                args->flash_mode = cli_parse_flash_mode(argv[++i]);
            } else {
                cli_show_error(JTAG_ERROR_INVALID_ARGS, "Option -m requires an argument");
                return JTAG_ERROR_INVALID_ARGS;
            }
        } else if (strcmp(arg, "-l") == 0 || strcmp(arg, "--log-level") == 0) {
            if (i + 1 < argc) {
                args->log_level = logger_parse_level(argv[++i]);
            } else {
                cli_show_error(JTAG_ERROR_INVALID_ARGS, "Option -l requires an argument");
                return JTAG_ERROR_INVALID_ARGS;
            }
        } else if (strcmp(arg, "--erase") == 0) {
            args->flash_mode = FLASH_MODE_ERASE;
        } else if (strcmp(arg, "--verify") == 0) {
            args->flash_mode = FLASH_MODE_VERIFY;
        } else if (strcmp(arg, "--read") == 0) {
            args->flash_mode = FLASH_MODE_READ;
        } else if (strcmp(arg, "--no-erase") == 0) {
            args->erase_before_write = false;
        } else if (strcmp(arg, "--no-verify") == 0) {
            args->verify_after_write = false;
        } else if (strcmp(arg, "--no-reset") == 0) {
            args->reset_after_write = false;
        } else if (arg[0] != '-') {
            // Non-option argument, assume it's firmware file
            if (strlen(args->firmware_file) == 0) {
                strncpy(args->firmware_file, arg, sizeof(args->firmware_file) - 1);
            }
        } else {
            cli_show_error(JTAG_ERROR_INVALID_ARGS, "Unknown option");
            printf("Use --help to see help information\n");
            return JTAG_ERROR_INVALID_ARGS;
        }
    }

    return JTAG_SUCCESS;
}

/**
 * Parse file type from string
 */
firmware_type_t cli_parse_file_type(const char* type_str) {
    if (!type_str) {
        return FIRMWARE_TYPE_AUTO;
    }

    if (strcasecmp(type_str, "bin") == 0) {
        return FIRMWARE_TYPE_BIN;
    } else if (strcasecmp(type_str, "hex") == 0) {
        return FIRMWARE_TYPE_HEX;
    } else if (strcasecmp(type_str, "elf") == 0) {
        return FIRMWARE_TYPE_ELF;
    } else if (strcasecmp(type_str, "s19") == 0 || strcasecmp(type_str, "srec") == 0) {
        return FIRMWARE_TYPE_S19;
    } else if (strcasecmp(type_str, "auto") == 0) {
        return FIRMWARE_TYPE_AUTO;
    }

    return FIRMWARE_TYPE_AUTO;
}

/**
 * Parse flash mode from string
 */
flash_mode_t cli_parse_flash_mode(const char* mode_str) {
    if (!mode_str) {
        return FLASH_MODE_WRITE;
    }

    if (strcasecmp(mode_str, "write") == 0) {
        return FLASH_MODE_WRITE;
    } else if (strcasecmp(mode_str, "verify") == 0) {
        return FLASH_MODE_VERIFY;
    } else if (strcasecmp(mode_str, "erase") == 0) {
        return FLASH_MODE_ERASE;
    } else if (strcasecmp(mode_str, "read") == 0) {
        return FLASH_MODE_READ;
    }

    return FLASH_MODE_WRITE;
}

/**
 * Parse address from string
 */
jtag_error_t cli_parse_address(const char* addr_str, uint32_t* address) {
    if (!addr_str || !address) {
        return JTAG_ERROR_INVALID_ARGS;
    }

    char* endptr;
    unsigned long addr = strtoul(addr_str, &endptr, 0);

    if (*endptr != '\0') {
        return JTAG_ERROR_INVALID_ARGS;
    }

    *address = (uint32_t)addr;
    return JTAG_SUCCESS;
}
